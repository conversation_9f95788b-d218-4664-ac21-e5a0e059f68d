/**
 * 图片预处理工具函数
 * 用于处理用户头像，确保在关系图谱中正确显示
 */

/**
 * 将任意形状的图片处理成带白色背景的圆形图片
 * @param imageUrl 原始图片URL
 * @returns Promise<string> 处理后的base64图片URL
 */
export const processAvatarImage = (imageUrl: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous'; // 处理跨域问题

    img.onload = () => {
      try {
        // 创建canvas元素
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('无法获取canvas上下文'));
          return;
        }

        // 设置canvas尺寸为正方形，使用固定尺寸确保一致性
        const size = 200; // 使用固定尺寸，确保所有头像大小一致
        canvas.width = size;
        canvas.height = size;

        // 创建圆形遮罩
        const radius = size / 2;
        const centerX = size / 2;
        const centerY = size / 2;

        // 清除画布并设置透明背景
        ctx.clearRect(0, 0, size, size);

        // 保存当前状态
        ctx.save();

        // 创建圆形裁剪路径
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        ctx.clip();

        // 在圆形裁剪区域内填充白色背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, size, size);

        // 计算图片缩放和居中位置，确保图片完全填充圆形区域
        const scale = Math.max(size / img.width, size / img.height);
        const scaledWidth = img.width * scale;
        const scaledHeight = img.height * scale;
        const offsetX = (size - scaledWidth) / 2;
        const offsetY = (size - scaledHeight) / 2;

        // 在圆形裁剪区域内绘制缩放后的图片
        ctx.drawImage(img, offsetX, offsetY, scaledWidth, scaledHeight);

        // 恢复状态
        ctx.restore();

        // 在圆形外添加一个细边框（可选）
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius - 1, 0, 2 * Math.PI);
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.lineWidth = 2;
        ctx.stroke();

        // 转换为base64格式
        const processedImageUrl = canvas.toDataURL('image/png', 0.9);
        console.log('🎨 图片处理完成:', {
          原始尺寸: `${img.width}x${img.height}`,
          处理后尺寸: `${size}x${size}`,
          缩放比例: scale.toFixed(2)
        });
        resolve(processedImageUrl);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = (error) => {
      console.error('🚫 图片加载失败:', { imageUrl, error });
      reject(new Error(`图片加载失败: ${imageUrl}`));
    };

    // 添加加载开始的日志
    console.log('🖼️ 开始处理图片:', imageUrl);
    img.src = imageUrl;
  });
};

/**
 * 批量处理多个头像图片
 * @param imageUrls 图片URL数组
 * @returns Promise<string[]> 处理后的图片URL数组
 */
export const processBatchAvatarImages = async (imageUrls: string[]): Promise<string[]> => {
  const promises = imageUrls.map((url) => processAvatarImage(url));
  return Promise.all(promises);
};

/**
 * 检查图片是否需要预处理
 * 如果图片已经是圆形或者是base64格式，可能不需要重复处理
 * @param imageUrl 图片URL
 * @returns boolean 是否需要预处理
 */
export const shouldProcessImage = (imageUrl: string): boolean => {
  // 如果已经是我们处理过的base64格式，不需要重复处理
  if (imageUrl.startsWith('data:image/png;base64,')) {
    return false;
  }

  // 如果是其他base64格式，仍然需要处理以确保圆形
  // 所有其他情况都需要处理成圆形
  return true;
};
